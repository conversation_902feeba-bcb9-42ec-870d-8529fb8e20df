# 数据标记任务 - 优化版本
# 使用优化的工具模块进行高效数据处理

from data_labeling_utils import quick_label_data, DataLabelingProcessor
import pandas as pd

print("数据标记任务 - 优化版本")
print("=" * 50)

# 方法1: 使用快速一键执行函数
print("\n方法1: 快速执行（推荐）")
print("-" * 30)

result = quick_label_data(
    main_file="result_20250617_111816.xlsx",
    label_file="/Users/<USER>/Downloads/0206样本.xlsx",
    output_prefix="result_20250617_111816_labeled",
    verbose=True
)

if result['success']:
    print(f"\n✓ 任务执行成功！")
    print(f"✓ 输出文件: {result['output_file']}")
    print(f"✓ 报告文件: {result['report_file']}")
    print(f"✓ 执行耗时: {result['duration']:.2f}秒")
    print(f"\n统计信息:")
    for key, value in result['stats'].items():
        print(f"  - {key}: {value}")
else:
    print(f"✗ 任务执行失败")
    if 'error' in result:
        print(f"错误信息: {result['error']}")

print("\n" + "=" * 50)


# 方法2: 使用高级处理器（适合需要自定义的场景）
print("\n方法2: 高级用法演示")
print("-" * 30)

# 创建处理器实例
processor = DataLabelingProcessor(verbose=True)

# 可以分步执行，便于调试和自定义
print("\n演示分步执行:")

# 步骤1: 加载数据
main_df, label_df = processor.load_and_validate_data(
    "result_20250616_152548.xlsx",
    "/Users/<USER>/Downloads/0206样本.xlsx"
)

if main_df is not None and label_df is not None:
    print(f"\n数据加载成功:")
    print(f"  - 主数据: {main_df.shape}")
    print(f"  - 标记数据: {label_df.shape}")
    
    # 可以在这里添加自定义的数据预处理逻辑
    # 例如：额外的数据清洗、特征工程等
    
    # 查看标签分布
    if '标签' in label_df.columns:
        print(f"\n标签分布:")
        label_counts = label_df['标签'].value_counts()
        for label, count in label_counts.items():
            print(f"  - {label}: {count}个")
    
    # 查看身份证号样本
    print(f"\n身份证号样本:")
    sample_ids = main_df['APP_IDCARD'].head(3).tolist()
    for i, idcard in enumerate(sample_ids, 1):
        print(f"  {i}. {idcard}")
        
else:
    print("数据加载失败，无法继续演示")

print("\n" + "-" * 50)
print("提示: 优化版本的主要改进:")
print("✓ 向量化操作提升性能")
print("✓ 更好的内存管理")
print("✓ 统一的日志系统")
print("✓ 模块化设计便于维护")
print("✓ 类型提示增强代码可读性")
print("✓ 完整的错误处理机制")


# 性能对比和使用建议
print("\n性能对比和使用建议")
print("-" * 30)

print("\n优化前 vs 优化后:")
print("┌─────────────────┬──────────────┬──────────────┐")
print("│ 项目            │ 优化前       │ 优化后       │")
print("├─────────────────┼──────────────┼──────────────┤")
print("│ 身份证号验证    │ 循环处理     │ 向量化操作   │")
print("│ 内存使用        │ 多次复制     │ 就地操作     │")
print("│ 错误处理        │ 基础try/catch│ 分类处理     │")
print("│ 代码结构        │ 单一文件     │ 模块化设计   │")
print("│ 日志系统        │ 简单print    │ 统一日志     │")
print("│ 类型安全        │ 无类型提示   │ 完整类型提示 │")
print("└─────────────────┴──────────────┴──────────────┘")

print("\n使用建议:")
print("• 小数据集(< 10万行): 两种方法性能差异不大")
print("• 大数据集(> 10万行): 建议使用优化版本")
print("• 生产环境: 强烈推荐使用优化版本")
print("• 调试阶段: 可以使用分步执行方法")

print("\n快速使用示例:")
print("```python")
print("from data_labeling_utils import quick_label_data")
print("")
print("result = quick_label_data(")
print("    main_file='your_main_file.xlsx',")
print("    label_file='your_label_file.xlsx'")
print(")")
print("```")


# 清理和总结
print("\n清理和总结")
print("-" * 30)

print("\n✅ 优化完成！主要改进包括:")
print("1. 创建了 data_labeling_utils.py 工具模块")
print("2. 实现了向量化的数据处理操作")
print("3. 优化了内存使用和性能")
print("4. 增强了错误处理和日志系统")
print("5. 提供了模块化和可重用的代码结构")

print("\n📁 文件结构:")
print("├── merge_bad_or_good.ipynb     # 优化后的主notebook")
print("├── data_labeling_utils.py      # 工具函数模块")
print("└── issues/数据标记任务.md      # 任务记录文档")

print("\n🚀 现在您可以:")
print("• 直接运行第一个cell进行快速数据标记")
print("• 使用工具模块在其他项目中重用代码")
print("• 根据需要自定义数据处理流程")

print("\n💡 下次使用时，只需要:")
print("```python")
print("from data_labeling_utils import quick_label_data")
print("result = quick_label_data('main.xlsx', 'label.xlsx')")
print("```")






