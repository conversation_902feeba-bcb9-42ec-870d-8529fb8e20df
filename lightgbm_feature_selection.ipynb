# LightGBM特征选择和数据降维

本notebook实现了基于LightGBM的高级特征选择功能，包括：
- 数据预处理和缺失值处理
- 数据不平衡处理
- 多种特征选择方法
- 特征重要性分析
- 性能评估和可视化


# 导入必要的库
import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import StratifiedKFold, cross_val_score
from sklearn.feature_selection import RFE, SelectKBest, f_classif
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import roc_auc_score, classification_report
from imblearn.over_sampling import SMOTE
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from datetime import datetime

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

print("✓ 库导入完成")


## 1. 数据加载和预处理


# 加载标记数据
input_file = "result_20250617_111816_labeled_20250617_112114.xlsx"

print("开始加载数据...")
df = pd.read_excel(input_file)
print(f"✓ 数据加载成功 - 形状: {df.shape}")

# 检查数据基本信息
print(f"\n数据概况:")
print(f"- 总行数: {df.shape[0]:,}")
print(f"- 总列数: {df.shape[1]:,}")
print(f"- 好坏标签分布:")
print(df['好坏标签'].value_counts())

# 检查-999缺失值
missing_999_count = (df == -999).sum().sum()
print(f"\n-999缺失值统计: {missing_999_count:,}个")


# 数据预处理函数
def preprocess_data(df):
    """
    数据预处理函数
    - 处理-999缺失值
    - 排除身份证号字段
    - 数据类型优化
    """
    print("开始数据预处理...")
    
    # 创建数据副本
    df_processed = df.copy()
    
    # 排除身份证号字段
    if 'APP_IDCARD' in df_processed.columns:
        df_processed = df_processed.drop('APP_IDCARD', axis=1)
        print("✓ 已排除APP_IDCARD字段")
    # 排除姓名和手机号
    if '姓名' in df_processed.columns:
        df_processed = df_processed.drop('姓名', axis=1)
        print("✓ 已排除姓名字段")
    if '手机号' in df_processed.columns:
        df_processed = df_processed.drop('手机号', axis=1)
        print("✓ 已排除手机号字段")
    # 分离特征和标签
    if '好坏标签' in df_processed.columns:
        y = df_processed['好坏标签'].copy()
        X = df_processed.drop('好坏标签', axis=1)
    else:
        raise ValueError("未找到'好坏标签'列")
    
    # 处理-999缺失值
    print("处理-999缺失值...")
    X_clean = X.replace(-999, np.nan)
    
    # 统计缺失值情况
    missing_stats = X_clean.isnull().sum()
    missing_cols = missing_stats[missing_stats > 0]
    print(f"✓ 有缺失值的列数: {len(missing_cols)}")
    print(f"✓ 总缺失值数量: {missing_stats.sum():,}")
    
    # 缺失值处理策略
    # 1. 删除缺失值过多的列（缺失率>80%）
    missing_ratio = missing_stats / len(X_clean)
    high_missing_cols = missing_ratio[missing_ratio > 0.8].index
    if len(high_missing_cols) > 0:
        X_clean = X_clean.drop(high_missing_cols, axis=1)
        print(f"✓ 删除了{len(high_missing_cols)}个高缺失率列")
    
    # 2. 对数值型特征用中位数填充
    numeric_cols = X_clean.select_dtypes(include=[np.number]).columns
    for col in numeric_cols:
        if X_clean[col].isnull().any():
            median_val = X_clean[col].median()
            X_clean[col].fillna(median_val, inplace=True)
    
    # 3. 对分类特征用众数填充
    categorical_cols = X_clean.select_dtypes(include=['object']).columns
    for col in categorical_cols:
        if X_clean[col].isnull().any():
            mode_val = X_clean[col].mode()[0] if not X_clean[col].mode().empty else 'unknown'
            X_clean[col].fillna(mode_val, inplace=True)
    
    # 处理分类变量编码
    label_encoders = {}
    for col in categorical_cols:
        if col in X_clean.columns:
            le = LabelEncoder()
            X_clean[col] = le.fit_transform(X_clean[col].astype(str))
            label_encoders[col] = le
    
    # 处理标签编码
    if y.dtype == 'object':
        label_encoder_y = LabelEncoder()
        y_encoded = label_encoder_y.fit_transform(y)
        print(f"✓ 标签编码: {dict(zip(label_encoder_y.classes_, label_encoder_y.transform(label_encoder_y.classes_)))}")
    else:
        y_encoded = y.values
        label_encoder_y = None
    
    print(f"✓ 预处理完成 - 最终特征数: {X_clean.shape[1]}, 样本数: {X_clean.shape[0]}")
    
    return X_clean, y_encoded, label_encoders, label_encoder_y

# 执行数据预处理
X_processed, y_processed, label_encoders, label_encoder_y = preprocess_data(df)

print(f"\n预处理后数据形状: {X_processed.shape}")
print(f"标签分布: {np.bincount(y_processed)}")

## 2. 数据不平衡处理


# 数据不平衡处理
def handle_imbalanced_data(X, y, method='smote', random_state=42):
    """
    处理数据不平衡问题
    
    Args:
        X: 特征数据
        y: 标签数据
        method: 处理方法 ('smote', 'none')
        random_state: 随机种子
    
    Returns:
        X_resampled, y_resampled: 重采样后的数据
    """
    print(f"原始数据分布: {np.bincount(y)}")
    
    if method == 'smote':
        print("使用SMOTE进行过采样...")
        # 计算合适的k_neighbors参数
        min_class_count = np.min(np.bincount(y))
        k_neighbors = min(5, min_class_count - 1) if min_class_count > 1 else 1
        
        smote = SMOTE(random_state=random_state, k_neighbors=k_neighbors)
        X_resampled, y_resampled = smote.fit_resample(X, y)
        print(f"✓ SMOTE完成 - 新数据分布: {np.bincount(y_resampled)}")
    else:
        print("不进行重采样处理")
        X_resampled, y_resampled = X, y
    
    return X_resampled, y_resampled

# 应用SMOTE处理数据不平衡
print("处理数据不平衡...")
X_balanced, y_balanced = handle_imbalanced_data(X_processed, y_processed, method='smote')

print(f"\n平衡后数据形状: {X_balanced.shape}")
print(f"平衡后标签分布: {np.bincount(y_balanced)}")

# 特征数量与性能关系分析
def plot_features_vs_performance(evaluation_df, figsize=(12, 6)):
    """
    绘制特征数量与性能的关系图
    
    Args:
        evaluation_df: 评估结果DataFrame
        figsize: 图形大小
    """
    # 过滤掉基准模型
    df_filtered = evaluation_df[~evaluation_df['method'].str.contains('baseline')].copy()
    
    # 提取方法类型
    df_filtered['method_type'] = df_filtered['method'].str.extract(r'([^_]+)_\d+')
    
    plt.figure(figsize=figsize)
    
    # 为不同方法类型使用不同颜色和标记
    method_styles = {
        'lgb': {'color': 'blue', 'marker': 'o', 'label': 'LightGBM重要性'},
        'rfe': {'color': 'red', 'marker': 's', 'label': '递归特征消除'},
        'statistical': {'color': 'green', 'marker': '^', 'label': '统计特征选择'}
    }
    
    for method_type, style in method_styles.items():
        method_data = df_filtered[df_filtered['method_type'] == method_type]
        if not method_data.empty:
            plt.errorbar(method_data['n_features'], method_data['cv_mean'], 
                        yerr=method_data['cv_std'], 
                        **style, markersize=8, linewidth=2, capsize=5)
    
    # 添加基准线
    baseline_score = evaluation_df[evaluation_df['method'].str.contains('baseline')]['cv_mean'].iloc[0]
    plt.axhline(y=baseline_score, color='black', linestyle='--', alpha=0.7, 
                label=f'基准模型 (所有特征): {baseline_score:.4f}')
    
    # 设置标题和标签
    plt.title('特征数量与模型性能关系', fontsize=16, fontweight='bold')
    plt.xlabel('特征数量', fontsize=12)
    plt.ylabel('AUC得分', fontsize=12)
    
    # 添加图例
    plt.legend()
    
    # 添加网格
    plt.grid(True, alpha=0.3)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('features_vs_performance.png', dpi=300, bbox_inches='tight')
    plt.show()

# 绘制特征数量与性能关系图
plot_features_vs_performance(evaluation_df)

## 3. LightGBM特征重要性分析


# LightGBM特征重要性分析
from sklearn.model_selection import train_test_split

def train_lightgbm_for_feature_importance(X, y, test_size=0.2, random_state=42):
    """
    训练LightGBM模型并获取特征重要性
    
    Args:
        X: 特征数据
        y: 标签数据
        test_size: 测试集比例
        random_state: 随机种子
    
    Returns:
        model: 训练好的模型
        feature_importance_df: 特征重要性DataFrame
        train_score: 训练集AUC
        test_score: 测试集AUC
    """
    print("训练LightGBM模型进行特征重要性分析...")
    
    # 分割数据
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=test_size, random_state=random_state, stratify=y
    )
    
    # LightGBM参数设置
    lgb_params = {
        'objective': 'binary',
        'metric': 'auc',
        'boosting_type': 'gbdt',
        'num_leaves': 31,
        'learning_rate': 0.05,
        'feature_fraction': 0.9,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'verbose': -1,
        'random_state': random_state
    }
    
    # 创建数据集
    train_data = lgb.Dataset(X_train, label=y_train)
    valid_data = lgb.Dataset(X_test, label=y_test, reference=train_data)
    
    # 训练模型
    model = lgb.train(
        lgb_params,
        train_data,
        valid_sets=[train_data, valid_data],
        valid_names=['train', 'eval'],
        num_boost_round=1000,
        callbacks=[lgb.early_stopping(stopping_rounds=50), lgb.log_evaluation(0)]
    )
    
    # 预测和评估
    train_pred = model.predict(X_train, num_iteration=model.best_iteration)
    test_pred = model.predict(X_test, num_iteration=model.best_iteration)
    
    train_score = roc_auc_score(y_train, train_pred)
    test_score = roc_auc_score(y_test, test_pred)
    
    print(f"✓ 模型训练完成")
    print(f"  - 训练集AUC: {train_score:.4f}")
    print(f"  - 测试集AUC: {test_score:.4f}")
    print(f"  - 最佳迭代次数: {model.best_iteration}")
    
    # 获取特征重要性
    feature_importance = model.feature_importance(importance_type='gain')
    feature_names = X.columns if hasattr(X, 'columns') else [f'feature_{i}' for i in range(X.shape[1])]
    
    feature_importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': feature_importance
    }).sort_values('importance', ascending=False)
    
    return model, feature_importance_df, train_score, test_score

# 训练模型并获取特征重要性
lgb_model, feature_importance_df, train_auc, test_auc = train_lightgbm_for_feature_importance(
    X_balanced, y_balanced
)

print(f"\n特征重要性分析完成")
print(f"前10个最重要的特征:")
print(feature_importance_df.head(10))

# 可视化特征重要性
def plot_feature_importance(feature_importance_df, top_n=20, figsize=(12, 8)):
    """
    绘制特征重要性图
    
    Args:
        feature_importance_df: 特征重要性DataFrame
        top_n: 显示前N个特征
        figsize: 图形大小
    """
    plt.figure(figsize=figsize)
    
    # 选择前N个特征
    top_features = feature_importance_df.head(top_n)
    
    # 创建水平条形图
    plt.barh(range(len(top_features)), top_features['importance'], 
             color='skyblue', edgecolor='navy', alpha=0.7)
    
    # 设置y轴标签
    plt.yticks(range(len(top_features)), top_features['feature'])
    
    # 反转y轴，使重要性最高的特征在顶部
    plt.gca().invert_yaxis()
    
    # 设置标题和标签
    plt.title(f'LightGBM特征重要性 (前{top_n}个特征)', fontsize=16, fontweight='bold')
    plt.xlabel('重要性得分', fontsize=12)
    plt.ylabel('特征名称', fontsize=12)
    
    # 添加网格
    plt.grid(axis='x', alpha=0.3)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('lightgbm_feature_importance.png', dpi=300, bbox_inches='tight')
    plt.show()

# 绘制特征重要性图
plot_feature_importance(feature_importance_df, top_n=20)

## 4. 多维度特征选择


# 多维度特征选择
def multi_dimensional_feature_selection(X, y, feature_importance_df, n_features_list=[50, 100, 200]):
    """
    多维度特征选择方法
    
    Args:
        X: 特征数据
        y: 标签数据
        feature_importance_df: LightGBM特征重要性
        n_features_list: 要选择的特征数量列表
    
    Returns:
        selection_results: 各种选择方法的结果
    """
    print("开始多维度特征选择...")
    
    selection_results = {}
    
    for n_features in n_features_list:
        print(f"\n=== 选择 {n_features} 个特征 ===")
        
        # 方法1: 基于LightGBM重要性选择
        print(f"方法1: LightGBM重要性选择")
        lgb_selected_features = feature_importance_df.head(n_features)['feature'].tolist()
        
        # 方法2: 递归特征消除 (RFE)
        print(f"方法2: 递归特征消除 (RFE)")
        lgb_estimator = lgb.LGBMClassifier(
            objective='binary',
            n_estimators=100,
            learning_rate=0.1,
            random_state=42,
            verbose=-1
        )
        
        rfe = RFE(estimator=lgb_estimator, n_features_to_select=n_features, step=0.1)
        rfe.fit(X, y)
        
        feature_names = X.columns if hasattr(X, 'columns') else [f'feature_{i}' for i in range(X.shape[1])]
        rfe_selected_features = [feature_names[i] for i in range(len(feature_names)) if rfe.support_[i]]
        
        # 方法3: 统计特征选择 (SelectKBest)
        print(f"方法3: 统计特征选择 (SelectKBest)")
        selector = SelectKBest(score_func=f_classif, k=n_features)
        selector.fit(X, y)
        
        statistical_selected_features = [feature_names[i] for i in range(len(feature_names)) if selector.get_support()[i]]
        
        # 保存结果
        selection_results[n_features] = {
            'lgb_importance': lgb_selected_features,
            'rfe': rfe_selected_features,
            'statistical': statistical_selected_features
        }
        
        print(f"✓ {n_features}个特征选择完成")
    
    return selection_results

# 执行多维度特征选择
selection_results = multi_dimensional_feature_selection(
    X_balanced, y_balanced, feature_importance_df, 
    n_features_list=[50, 100, 200]
)

print("\n多维度特征选择完成")

# 特征选择验证和性能评估
def evaluate_feature_selection(X, y, selection_results, cv_folds=5):
    """
    评估不同特征选择方法的性能
    
    Args:
        X: 原始特征数据
        y: 标签数据
        selection_results: 特征选择结果
        cv_folds: 交叉验证折数
    
    Returns:
        evaluation_results: 评估结果
    """
    print("开始特征选择性能评估...")
    
    evaluation_results = []
    
    # 基准模型（使用所有特征）
    print("\n评估基准模型（所有特征）...")
    lgb_classifier = lgb.LGBMClassifier(
        objective='binary',
        n_estimators=200,
        learning_rate=0.05,
        random_state=42,
        verbose=-1
    )
    
    cv_scores = cross_val_score(lgb_classifier, X, y, cv=cv_folds, scoring='roc_auc')
    baseline_score = cv_scores.mean()
    baseline_std = cv_scores.std()
    
    evaluation_results.append({
        'method': 'baseline_all_features',
        'n_features': X.shape[1],
        'cv_mean': baseline_score,
        'cv_std': baseline_std
    })
    
    print(f"基准模型 AUC: {baseline_score:.4f} ± {baseline_std:.4f}")
    
    # 评估各种特征选择方法
    for n_features, methods in selection_results.items():
        print(f"\n=== 评估 {n_features} 个特征的选择方法 ===")
        
        for method_name, selected_features in methods.items():
            print(f"评估方法: {method_name}")
            
            # 选择特征
            if hasattr(X, 'columns'):
                X_selected = X[selected_features]
            else:
                feature_indices = [i for i, name in enumerate([f'feature_{i}' for i in range(X.shape[1])]) if name in selected_features]
                X_selected = X[:, feature_indices]
            
            # 交叉验证评估
            cv_scores = cross_val_score(lgb_classifier, X_selected, y, cv=cv_folds, scoring='roc_auc')
            mean_score = cv_scores.mean()
            std_score = cv_scores.std()
            
            evaluation_results.append({
                'method': f'{method_name}_{n_features}',
                'n_features': len(selected_features),
                'cv_mean': mean_score,
                'cv_std': std_score,
                'improvement': mean_score - baseline_score
            })
            
            print(f"  AUC: {mean_score:.4f} ± {std_score:.4f} (改进: {mean_score - baseline_score:+.4f})")
    
    return pd.DataFrame(evaluation_results)

# 执行特征选择性能评估
evaluation_df = evaluate_feature_selection(X_balanced, y_balanced, selection_results)

print("\n=== 特征选择性能评估结果 ===")
print(evaluation_df.sort_values('cv_mean', ascending=False))

## 5. 结果可视化和分析


# 性能对比可视化
def plot_performance_comparison(evaluation_df, figsize=(14, 8)):
    """
    绘制不同特征选择方法的性能对比图
    
    Args:
        evaluation_df: 评估结果DataFrame
        figsize: 图形大小
    """
    plt.figure(figsize=figsize)
    
    # 排序数据
    df_sorted = evaluation_df.sort_values('cv_mean', ascending=True)
    
    # 创建颜色映射
    colors = ['red' if 'baseline' in method else 'skyblue' for method in df_sorted['method']]
    
    # 绘制水平条形图
    bars = plt.barh(range(len(df_sorted)), df_sorted['cv_mean'], 
                    xerr=df_sorted['cv_std'], color=colors, alpha=0.7, 
                    edgecolor='navy', capsize=5)
    
    # 设置y轴标签
    plt.yticks(range(len(df_sorted)), df_sorted['method'])
    
    # 设置标题和标签
    plt.title('特征选择方法性能对比 (AUC得分)', fontsize=16, fontweight='bold')
    plt.xlabel('AUC得分', fontsize=12)
    plt.ylabel('特征选择方法', fontsize=12)
    
    # 添加数值标签
    for i, (bar, score, std) in enumerate(zip(bars, df_sorted['cv_mean'], df_sorted['cv_std'])):
        plt.text(bar.get_width() + 0.001, bar.get_y() + bar.get_height()/2, 
                f'{score:.4f}±{std:.3f}', 
                ha='left', va='center', fontsize=10)
    
    # 添加网格
    plt.grid(axis='x', alpha=0.3)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('feature_selection_performance_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

# 绘制性能对比图
plot_performance_comparison(evaluation_df)

# 特征数量与性能关系分析
def plot_features_vs_performance(evaluation_df, figsize=(12, 6)):
    """
    绘制特征数量与性能的关系图
    
    Args:
        evaluation_df: 评估结果DataFrame
        figsize: 图形大小
    """
    # 过滤掉基准模型
    df_filtered = evaluation_df[~evaluation_df['method'].str.contains('baseline')].copy()
    
    # 提取方法类型
    df_filtered['method_type'] = df_filtered['method'].str.extract(r'([^_]+)_\d+')
    
    plt.figure(figsize=figsize)
    
    # 为不同方法类型使用不同颜色和标记
    method_styles = {
        'lgb': {'color': 'blue', 'marker': 'o', 'label': 'LightGBM重要性'},
        'rfe': {'color': 'red', 'marker': 's', 'label': '递归特征消除'},
        'statistical': {'color': 'green', 'marker': '^', 'label': '统计特征选择'}
    }
    
    for method_type, style in method_styles.items():
        method_data = df_filtered[df_filtered['method_type'] == method_type]
        if not method_data.empty:
            plt.errorbar(method_data['n_features'], method_data['cv_mean'], 
                        yerr=method_data['cv_std'], 
                        **style, markersize=8, linewidth=2, capsize=5)
    
    # 添加基准线
    baseline_score = evaluation_df[evaluation_df['method'].str.contains('baseline')]['cv_mean'].iloc[0]
    plt.axhline(y=baseline_score, color='black', linestyle='--', alpha=0.7, 
                label=f'基准模型 (所有特征): {baseline_score:.4f}')
    
    # 设置标题和标签
    plt.title('特征数量与模型性能关系', fontsize=16, fontweight='bold')
    plt.xlabel('特征数量', fontsize=12)
    plt.ylabel('AUC得分', fontsize=12)
    
    # 添加图例
    plt.legend()
    
    # 添加网格
    plt.grid(True, alpha=0.3)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('features_vs_performance.png', dpi=300, bbox_inches='tight')
    plt.show()

# 绘制特征数量与性能关系图
plot_features_vs_performance(evaluation_df)

## 6. 结果导出和报告生成


# 生成最终报告和导出结果
def generate_final_report(feature_importance_df, selection_results, evaluation_df, 
                         output_file='lightgbm_feature_selection_results.xlsx'):
    """
    生成最终的特征选择报告
    
    Args:
        feature_importance_df: 特征重要性DataFrame
        selection_results: 特征选择结果
        evaluation_df: 性能评估结果
        output_file: 输出文件名
    """
    print(f"生成最终报告: {output_file}")
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 1. 特征重要性排序
        feature_importance_df.to_excel(writer, sheet_name='特征重要性排序', index=False)
        
        # 2. 性能评估结果
        evaluation_df.sort_values('cv_mean', ascending=False).to_excel(
            writer, sheet_name='性能评估结果', index=False
        )
        
        # 3. 各种方法选择的特征列表
        for n_features, methods in selection_results.items():
            sheet_name = f'选择{n_features}个特征'
            
            # 创建对比表
            max_len = max(len(features) for features in methods.values())
            comparison_data = {}
            
            for method_name, features in methods.items():
                # 补齐长度
                padded_features = features + [''] * (max_len - len(features))
                comparison_data[method_name] = padded_features
            
            comparison_df = pd.DataFrame(comparison_data)
            comparison_df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        # 4. 推荐的最佳特征集
        best_method = evaluation_df.loc[evaluation_df['cv_mean'].idxmax()]
        best_method_name = best_method['method']
        
        # 解析最佳方法的特征数量和类型
        if 'baseline' not in best_method_name:
            method_parts = best_method_name.split('_')
            # method_type = method_parts[0]
            # n_features_best = int(method_parts[1])
            
            if len(method_parts) == 3:
                method_type = '_'.join(method_parts[:2])  # "lgb_importance"
                n_features_best = int(method_parts[2])  
            else:
                method_type = method_parts[0]
                n_features_best = int(method_parts[1])
            best_features = selection_results[n_features_best][method_type]
            
            # 获取这些特征的重要性信息
            best_features_info = feature_importance_df[
                feature_importance_df['feature'].isin(best_features)
            ].copy()
            
            best_features_info.to_excel(writer, sheet_name='推荐特征集', index=False)
    
    print(f"✓ 报告已保存到: {output_file}")
    
    # 打印总结信息
    print("\n" + "="*60)
    print("LightGBM特征选择分析总结")
    print("="*60)
    
    print(f"\n📊 数据概况:")
    print(f"  - 原始特征数量: {len(feature_importance_df)}")
    print(f"  - 样本数量: {X_balanced.shape[0]:,}")
    print(f"  - 标签分布: {dict(zip(*np.unique(y_balanced, return_counts=True)))}")
    
    print(f"\n🏆 最佳性能方法:")
    best_row = evaluation_df.loc[evaluation_df['cv_mean'].idxmax()]
    print(f"  - 方法: {best_row['method']}")
    print(f"  - 特征数量: {best_row['n_features']}")
    print(f"  - AUC得分: {best_row['cv_mean']:.4f} ± {best_row['cv_std']:.4f}")
    if 'improvement' in best_row:
        print(f"  - 相比基准改进: {best_row['improvement']:+.4f}")
    
    print(f"\n📈 前5个最重要特征:")
    for i, row in feature_importance_df.head(5).iterrows():
        print(f"  {i+1}. {row['feature']}: {row['importance']:.2f}")
    
    print(f"\n💾 输出文件:")
    print(f"  - 详细报告: {output_file}")
    print(f"  - 特征重要性图: lightgbm_feature_importance.png")
    print(f"  - 性能对比图: feature_selection_performance_comparison.png")
    print(f"  - 特征数量关系图: features_vs_performance.png")
    
    print("\n✅ LightGBM特征选择分析完成！")

# 生成最终报告
generate_final_report(feature_importance_df, selection_results, evaluation_df)

# 修复版本：如果上面的报告生成出错，使用这个简化版本
def generate_simple_report(feature_importance_df, selection_results, evaluation_df, 
                          output_file='lightgbm_feature_selection_results_simple.xlsx'):
    """
    生成简化版本的特征选择报告（修复版本）
    """
    print(f"生成简化报告: {output_file}")
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 1. 特征重要性排序
        feature_importance_df.to_excel(writer, sheet_name='特征重要性排序', index=False)
        
        # 2. 性能评估结果
        evaluation_df.sort_values('cv_mean', ascending=False).to_excel(
            writer, sheet_name='性能评估结果', index=False
        )
        
        # 3. 各种方法选择的特征列表
        for n_features, methods in selection_results.items():
            sheet_name = f'选择{n_features}个特征'
            
            # 创建对比表
            max_len = max(len(features) for features in methods.values())
            comparison_data = {}
            
            for method_name, features in methods.items():
                # 补齐长度
                padded_features = features + [''] * (max_len - len(features))
                comparison_data[method_name] = padded_features
            
            comparison_df = pd.DataFrame(comparison_data)
            comparison_df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        # 4. 推荐特征集（使用前100个最重要特征）
        recommended_features = feature_importance_df.head(100).copy()
        recommended_features.to_excel(writer, sheet_name='推荐特征集', index=False)
    
    print(f"✓ 简化报告已保存到: {output_file}")
    
    # 打印总结信息
    print("\n" + "="*60)
    print("LightGBM特征选择分析总结（简化版）")
    print("="*60)
    
    print(f"\n📊 数据概况:")
    print(f"  - 原始特征数量: {len(feature_importance_df)}")
    
    print(f"\n🏆 最佳性能方法:")
    best_row = evaluation_df.loc[evaluation_df['cv_mean'].idxmax()]
    print(f"  - 方法: {best_row['method']}")
    print(f"  - 特征数量: {best_row['n_features']}")
    print(f"  - AUC得分: {best_row['cv_mean']:.4f} ± {best_row['cv_std']:.4f}")
    
    print(f"\n📈 前5个最重要特征:")
    for i, row in feature_importance_df.head(5).iterrows():
        print(f"  {i+1}. {row['feature']}: {row['importance']:.2f}")
    
    print(f"\n💾 输出文件:")
    print(f"  - 简化报告: {output_file}")
    
    print("\n✅ 简化版LightGBM特征选择分析完成！")

# 如果上面的报告生成失败，运行这个
# generate_simple_report(feature_importance_df, selection_results, evaluation_df)

# 可选: 使用最佳特征集训练最终模型
def train_final_model_with_best_features(X, y, feature_importance_df, n_best_features=100):
    """
    使用最佳特征集训练最终模型
    
    Args:
        X: 特征数据
        y: 标签数据
        feature_importance_df: 特征重要性
        n_best_features: 使用的最佳特征数量
    
    Returns:
        final_model: 最终训练的模型
        selected_features: 选择的特征列表
    """
    print(f"使用前{n_best_features}个最重要特征训练最终模型...")
    
    # 选择最重要的特征
    best_features = feature_importance_df.head(n_best_features)['feature'].tolist()
    
    if hasattr(X, 'columns'):
        X_final = X[best_features]
    else:
        feature_indices = [i for i, name in enumerate([f'feature_{i}' for i in range(X.shape[1])]) if name in best_features]
        X_final = X[:, feature_indices]
    
    # 分割数据
    X_train, X_test, y_train, y_test = train_test_split(
        X_final, y, test_size=0.2, random_state=42, stratify=y
    )
    
    # 训练最终模型
    final_model = lgb.LGBMClassifier(
        objective='binary',
        n_estimators=500,
        learning_rate=0.05,
        num_leaves=31,
        feature_fraction=0.9,
        bagging_fraction=0.8,
        bagging_freq=5,
        random_state=42,
        verbose=-1
    )
    
    final_model.fit(X_train, y_train)
    
    # 评估最终模型
    train_pred = final_model.predict_proba(X_train)[:, 1]
    test_pred = final_model.predict_proba(X_test)[:, 1]
    
    train_auc = roc_auc_score(y_train, train_pred)
    test_auc = roc_auc_score(y_test, test_pred)
    
    print(f"✓ 最终模型训练完成")
    print(f"  - 使用特征数: {len(best_features)}")
    print(f"  - 训练集AUC: {train_auc:.4f}")
    print(f"  - 测试集AUC: {test_auc:.4f}")
    
    return final_model, best_features

# 训练最终模型（可选）
print("\n" + "="*50)
print("训练最终优化模型")
print("="*50)

final_model, final_features = train_final_model_with_best_features(
    X_balanced, y_balanced, feature_importance_df, n_best_features=100
)

print(f"\n🎯 最终模型使用的特征:")
print(f"前10个特征: {final_features[:10]}")
print(f"...等共{len(final_features)}个特征")