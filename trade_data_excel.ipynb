# 交易数据导出到Excel

本notebook用于从数据库查询交易数据并导出为Excel文件。


# 导入必要的库
from sqlalchemy import create_engine
import pandas as pd
import json
from datetime import datetime


# 创建数据库连接
engine = create_engine('mysql+pymysql://root:root%402024@192.168.2.103:3306/smartdecision?charset=utf8mb4')


# 查询交易服务器数据
sql1 = """
SELECT ctsd.SERIAL_NUMBER, ctl.APP_IDCARD, ctsd.RESOURCE_CODE, ctsd.SERVER_RESPONSE_DATA
FROM crd_trade_server_data ctsd
INNER JOIN crd_trade_log ctl ON ctsd.SERIAL_NUMBER = ctl.SERIAL_NUMBER
WHERE ctl.PRODUCT_CODE = '202088888820250206093418' ;
"""
df_data = pd.read_sql(sql1, engine)
print(f"查询到 {len(df_data)} 条交易数据")
df_data

# 查询数据源参数配置
sql2 = """
select RESOURCE_CODE, ENAME, CNAME 
from crd_datasource_params 
where FIELD_TYPE='2' and VERSION='V1' 
and RESOURCE_CODE not in ('yinkesit10001', '976652b23d41493c89396367fbfba2a9', 'test001', 'fin012', 'fin003') 
and CNAME not in ('返回码', '返回代码', '错误码', '状态') 
and ENAME not in ('supplement_case_list','loan_willingness_profile','execution_limited','execution_pro','loan_rate_profile') 
order by RESOURCE_CODE;
"""
df_params = pd.read_sql(sql2, engine)
print(f"查询到 {len(df_params)} 个字段参数")
df_params

# 生成唯一列名，处理重复的CNAME
cname_counts = df_params['CNAME'].value_counts()

def get_unique_key(row):
    if cname_counts[row['CNAME']] > 1:
        return f"{row['CNAME']}({row['RESOURCE_CODE']})"
    else:
        return row['CNAME']

df_params['UNIQUE_KEY'] = df_params.apply(get_unique_key, axis=1)
# 保留输出用的列名
df_params['OUTPUT_CNAME'] = df_params['ENAME']

# 创建参数映射字典 (RESOURCE_CODE, ENAME) -> 数据处理用KEY
param_map = {(row['RESOURCE_CODE'], row['ENAME']): row['UNIQUE_KEY'] for _, row in df_params.iterrows()}
print(f"创建了 {len(param_map)} 个参数映射")
param_map


# 数据预处理：解析JSON数据并规范化空值
data_map = {}

for _, row in df_data.iterrows():
    idcard = row['APP_IDCARD']  # 改为使用身份证号码
    rc = row['RESOURCE_CODE']
    try:
        data = json.loads(row['SERVER_RESPONSE_DATA'])
        def normalize_null(v):
            if v is None:
                return -999
            if isinstance(v, str) and v.strip().lower().replace('"', '') == 'null':
                return -999
            return v
        data = {k: normalize_null(v) for k, v in data.items()}
    except Exception:
        data = {}
    data_map[(idcard, rc)] = data

print(f"处理了 {len(data_map)} 条数据记录")
data_map

# 生成结果数据：按身份证号码组织数据
app_idcards = df_data['APP_IDCARD'].unique()
result_rows = []

for idcard in app_idcards:
    row_dict = {'APP_IDCARD': idcard}  # 改为身份证号码
    for _, row in df_params.iterrows():
        rc = row['RESOURCE_CODE']
        en = row['ENAME']
        col_key = row['UNIQUE_KEY']
        data = data_map.get((idcard, rc), None)
        value = data.get(en) if data and en in data else None
        row_dict[col_key] = value
    result_rows.append(row_dict)

print(f"生成了 {len(result_rows)} 行结果数据")
result_rows


# 构建输出DataFrame并设置列标题
columns = ['APP_IDCARD'] + list(df_params['UNIQUE_KEY'])  # 改为身份证号码
output_df = pd.DataFrame(result_rows, columns=columns)

# 设置输出列标题（全部用CNAME而不带(RESOURCE_CODE)）
# col_rename = {'APP_IDCARD': '身份证号码'}  # 设置中文列名
# for _, row in df_params.iterrows():
#     col_rename[row['UNIQUE_KEY']] = row['OUTPUT_CNAME']

# output_df.rename(columns=col_rename, inplace=True)
print(f"输出DataFrame形状: {output_df.shape}")
output_df.head()

# 导出到Excel文件
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
filename = f"result_{timestamp}.xlsx"
output_df.to_excel(filename, index=False)
print("处理完成，结果已保存为 " + filename)
